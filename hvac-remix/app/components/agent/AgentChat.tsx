/**
 * Agent Chat Component
 * Real-time chat interface for interacting with HVAC agents
 */

import { useState, useEffect, useRef } from 'react';
import { useFetcher } from '@remix-run/react';

interface AgentChatProps {
  agentId: string;
  agentType: 'customer_service' | 'service_order' | 'document_analysis';
  customerId?: string;
  serviceOrderId?: string;
  documentId?: string;
  initialContext?: any;
  onResponse?: (response: string) => void;
  className?: string;
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  isLoading?: boolean;
}

export function AgentChat({
  agentId,
  agentType,
  customerId,
  serviceOrderId,
  documentId,
  initialContext,
  onResponse,
  className = '',
}: AgentChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [threadId, setThreadId] = useState<string | null>(null);
  const [currentRunId, setCurrentRunId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const fetcher = useFetcher();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Initialize chat thread
  useEffect(() => {
    initializeChat();
  }, [agentId, customerId, serviceOrderId, documentId]);

  // Poll for agent responses
  useEffect(() => {
    let pollInterval: NodeJS.Timeout;
    
    if (currentRunId && threadId) {
      pollInterval = setInterval(async () => {
        await checkRunStatus();
      }, 2000); // Poll every 2 seconds
    }

    return () => {
      if (pollInterval) {
        clearInterval(pollInterval);
      }
    };
  }, [currentRunId, threadId]);

  const initializeChat = async () => {
    if (!agentId) return;

    try {
      const response = await fetch('/api/agent/chat/initialize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agentId,
          agentType,
          customerId,
          serviceOrderId,
          documentId,
          initialContext,
        }),
      });

      const data = await response.json();
      
      if (data.success && data.threadId) {
        setThreadId(data.threadId);
        
        // Add welcome message
        const welcomeMessage: ChatMessage = {
          id: 'welcome',
          role: 'assistant',
          content: getWelcomeMessage(agentType),
          timestamp: new Date(),
        };
        setMessages([welcomeMessage]);
      }
    } catch (error) {
      console.error('Error initializing chat:', error);
    }
  };

  const getWelcomeMessage = (type: string): string => {
    switch (type) {
      case 'customer_service':
        return 'Hello! I\'m your HVAC customer service assistant. I can help you with customer analysis, service recommendations, and answering questions about HVAC systems. How can I assist you today?';
      case 'service_order':
        return 'Hi! I\'m your HVAC service order assistant. I can help optimize routes, analyze service history, predict maintenance needs, and generate service reports. What would you like me to help you with?';
      case 'document_analysis':
        return 'Greetings! I\'m your HVAC document analysis assistant. I can analyze manuals, process invoices, examine equipment photos, and extract service instructions. Please share a document or ask me a question!';
      default:
        return 'Hello! I\'m your HVAC assistant. How can I help you today?';
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || !threadId || isLoading) return;

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    // Add loading message
    const loadingMessage: ChatMessage = {
      id: `loading-${Date.now()}`,
      role: 'assistant',
      content: 'Thinking...',
      timestamp: new Date(),
      isLoading: true,
    };
    setMessages(prev => [...prev, loadingMessage]);

    try {
      const response = await fetch('/api/agent/chat/message', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agentId,
          threadId,
          message: userMessage.content,
          context: {
            customerId,
            serviceOrderId,
            documentId,
            agentType,
          },
        }),
      });

      const data = await response.json();
      
      if (data.success && data.runId) {
        setCurrentRunId(data.runId);
      } else {
        // Remove loading message and show error
        setMessages(prev => prev.filter(msg => !msg.isLoading));
        const errorMessage: ChatMessage = {
          id: `error-${Date.now()}`,
          role: 'assistant',
          content: 'Sorry, I encountered an error processing your message. Please try again.',
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, errorMessage]);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(prev => prev.filter(msg => !msg.isLoading));
      setIsLoading(false);
    }
  };

  const checkRunStatus = async () => {
    if (!currentRunId || !threadId) return;

    try {
      const response = await fetch(`/api/agent/chat/status?runId=${currentRunId}&threadId=${threadId}&agentId=${agentId}`);
      const data = await response.json();

      if (data.success) {
        if (data.status === 'completed' && data.messages) {
          // Remove loading message
          setMessages(prev => prev.filter(msg => !msg.isLoading));
          
          // Add agent response
          const agentMessages = data.messages
            .filter((msg: any) => msg.role === 'assistant')
            .map((msg: any) => ({
              id: msg.message_id,
              role: 'assistant' as const,
              content: msg.content,
              timestamp: new Date(msg.created_at),
            }));

          setMessages(prev => [...prev, ...agentMessages]);
          
          // Call onResponse callback if provided
          if (onResponse && agentMessages.length > 0) {
            onResponse(agentMessages[agentMessages.length - 1].content);
          }
          
          setCurrentRunId(null);
          setIsLoading(false);
        } else if (data.status === 'failed') {
          // Remove loading message and show error
          setMessages(prev => prev.filter(msg => !msg.isLoading));
          const errorMessage: ChatMessage = {
            id: `error-${Date.now()}`,
            role: 'assistant',
            content: 'I encountered an error processing your request. Please try again or rephrase your question.',
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, errorMessage]);
          setCurrentRunId(null);
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.error('Error checking run status:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTimestamp = (timestamp: Date): string => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className={`flex flex-col h-full bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50 rounded-t-lg">
        <div className="flex items-center space-x-3">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <h3 className="font-semibold text-gray-900">
            {agentType === 'customer_service' && 'Customer Service Agent'}
            {agentType === 'service_order' && 'Service Order Agent'}
            {agentType === 'document_analysis' && 'Document Analysis Agent'}
          </h3>
        </div>
        <div className="text-sm text-gray-500">
          {threadId ? 'Connected' : 'Connecting...'}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : message.isLoading
                  ? 'bg-gray-100 text-gray-600 animate-pulse'
                  : 'bg-gray-100 text-gray-900'
              }`}
            >
              <div className="text-sm">{message.content}</div>
              <div
                className={`text-xs mt-1 ${
                  message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                }`}
              >
                {formatTimestamp(message.timestamp)}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            className="flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={1}
            disabled={isLoading}
          />
          <button
            onClick={sendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );
}
