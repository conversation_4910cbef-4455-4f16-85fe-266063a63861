import React, { forwardRef } from "react";

interface ServiceReportData {
  id: string;
  title: string;
  description?: string | null;
  workPerformed?: string | null;
  partsUsed?: string | null;
  recommendations?: string | null;
  technicianSignatureUrl?: string | null;
  customerSignatureUrl?: string | null;
  signedAt?: string | null;
  photoUrls?: string[] | null;
  createdAt: string;
  serviceOrder?: {
    id: string;
    title: string;
    description?: string | null;
    status: string;
    priority: string;
    type: string;
    scheduledDate?: string | null;
    completedDate?: string | null;
    customer?: {
      id: string;
      name: string;
      email?: string | null;
      phone?: string | null;
      address?: string | null;
      city?: string | null;
      postalCode?: string | null;
      country?: string | null;
    } | null;
    device?: {
      id: string;
      name: string;
      model?: string | null;
      serialNumber?: string | null;
      manufacturer?: string | null;
      type?: string | null;
    } | null;
  } | null;
  companyInfo?: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website: string;
    logo?: string;
    reportFooter?: string;
  };
}

interface PrintableServiceReportProps {
  data: ServiceReportData;
  showPrintButton?: boolean;
}

export const PrintableServiceReport = forwardRef<HTMLDivElement, PrintableServiceReportProps>(
  ({ data, showPrintButton = false }, ref) => {
    const formatDate = (dateString: string | null | undefined) => {
      if (!dateString) return "N/A";
      return new Date(dateString).toLocaleDateString();
    };

    const handlePrint = () => {
      window.print();
    };

    // Parse photo URLs
    const photos = data.photoUrls 
      ? (typeof data.photoUrls === 'string' ? JSON.parse(data.photoUrls) : data.photoUrls) 
      : [];

    return (
      <div ref={ref} className="bg-white p-8 max-w-4xl mx-auto">
        {showPrintButton && (
          <div className="print:hidden mb-4 text-right">
            <button
              onClick={handlePrint}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            >
              Print Report
            </button>
          </div>
        )}

        {/* Header */}
        <div className="flex justify-between items-start mb-8 border-b pb-6">
          <div>
            {data.companyInfo?.logo ? (
              <img 
                src={data.companyInfo.logo} 
                alt="Company Logo" 
                className="h-16 mb-2" 
              />
            ) : (
              <h1 className="text-2xl font-bold">
                {data.companyInfo?.name || "HVAC Service Company"}
              </h1>
            )}
            <div className="text-gray-600">
              <p>{data.companyInfo?.address || "123 Main St, City, State 12345"}</p>
              <p>
                {data.companyInfo?.phone || "************"} | 
                {data.companyInfo?.email || "<EMAIL>"}
              </p>
              <p>{data.companyInfo?.website || "www.hvaccompany.com"}</p>
            </div>
          </div>
          <div className="text-right">
            <h2 className="text-xl font-bold">Service Report</h2>
            <p className="text-gray-600">Report #: {data.id}</p>
            <p className="text-gray-600">Date: {formatDate(data.createdAt)}</p>
            {data.serviceOrder?.completedDate && (
              <p className="text-gray-600">
                Completion Date: {formatDate(data.serviceOrder.completedDate)}
              </p>
            )}
          </div>
        </div>

        {/* Customer & Device Information */}
        <div className="grid grid-cols-2 gap-6 mb-8">
          <div className="border p-4 rounded">
            <h3 className="font-bold text-lg mb-2">Customer Information</h3>
            {data.serviceOrder?.customer ? (
              <div>
                <p className="font-semibold">{data.serviceOrder.customer.name}</p>
                {data.serviceOrder.customer.address && (
                  <p>{data.serviceOrder.customer.address}</p>
                )}
                {data.serviceOrder.customer.city && data.serviceOrder.customer.postalCode && (
                  <p>
                    {data.serviceOrder.customer.city}, {data.serviceOrder.customer.postalCode}
                    {data.serviceOrder.customer.country ? `, ${data.serviceOrder.customer.country}` : ""}
                  </p>
                )}
                {data.serviceOrder.customer.phone && <p>Phone: {data.serviceOrder.customer.phone}</p>}
                {data.serviceOrder.customer.email && <p>Email: {data.serviceOrder.customer.email}</p>}
              </div>
            ) : (
              <p className="text-gray-500">No customer information available</p>
            )}
          </div>

          <div className="border p-4 rounded">
            <h3 className="font-bold text-lg mb-2">Device Information</h3>
            {data.serviceOrder?.device ? (
              <div>
                <p className="font-semibold">{data.serviceOrder.device.name}</p>
                {data.serviceOrder.device.model && <p>Model: {data.serviceOrder.device.model}</p>}
                {data.serviceOrder.device.serialNumber && <p>S/N: {data.serviceOrder.device.serialNumber}</p>}
                {data.serviceOrder.device.manufacturer && <p>Manufacturer: {data.serviceOrder.device.manufacturer}</p>}
                {data.serviceOrder.device.type && <p>Type: {data.serviceOrder.device.type}</p>}
              </div>
            ) : (
              <p className="text-gray-500">No device information available</p>
            )}
          </div>
        </div>

        {/* Service Information */}
        <div className="mb-8">
          <h3 className="font-bold text-lg mb-2 border-b pb-1">Service Information</h3>
          <div className="mb-4">
            <p className="font-semibold">Service Type:</p>
            <p>{data.serviceOrder?.type || "N/A"}</p>
          </div>
          <div className="mb-4">
            <p className="font-semibold">Description:</p>
            <p className="whitespace-pre-line">{data.description || data.serviceOrder?.description || "N/A"}</p>
          </div>
          <div className="mb-4">
            <p className="font-semibold">Work Performed:</p>
            <p className="whitespace-pre-line">{data.workPerformed || "N/A"}</p>
          </div>
          <div className="mb-4">
            <p className="font-semibold">Parts Used:</p>
            <p className="whitespace-pre-line">{data.partsUsed || "N/A"}</p>
          </div>
          <div className="mb-4">
            <p className="font-semibold">Recommendations:</p>
            <p className="whitespace-pre-line">{data.recommendations || "N/A"}</p>
          </div>
        </div>

        {/* Photos */}
        {photos.length > 0 && (
          <div className="mb-8">
            <h3 className="font-bold text-lg mb-2 border-b pb-1">Photos</h3>
            <div className="grid grid-cols-2 gap-4">
              {photos.map((url: string, index: number) => (
                <div key={index} className="border p-2">
                  <img src={url} alt={`Service photo ${index + 1}`} className="w-full h-auto" />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Signatures */}
        <div className="grid grid-cols-2 gap-6 mb-8">
          <div className="border p-4 rounded">
            <h3 className="font-bold text-lg mb-2">Technician Signature</h3>
            {data.technicianSignatureUrl ? (
              <img 
                src={data.technicianSignatureUrl} 
                alt="Technician Signature" 
                className="max-h-24" 
              />
            ) : (
              <div className="border-b border-gray-400 h-16 flex items-end">
                <p className="text-gray-400">Signature not available</p>
              </div>
            )}
          </div>

          <div className="border p-4 rounded">
            <h3 className="font-bold text-lg mb-2">Customer Signature</h3>
            {data.customerSignatureUrl ? (
              <img 
                src={data.customerSignatureUrl} 
                alt="Customer Signature" 
                className="max-h-24" 
              />
            ) : (
              <div className="border-b border-gray-400 h-16 flex items-end">
                <p className="text-gray-400">Signature not available</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-gray-500 text-sm mt-8 pt-4 border-t">
          <p>{data.companyInfo?.reportFooter || "We appreciate your trust in our services."}</p>
          <p>This report was generated on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}</p>
        </div>
      </div>
    );
  }
);

PrintableServiceReport.displayName = "PrintableServiceReport";
