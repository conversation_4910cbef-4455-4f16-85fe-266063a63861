import React, { forwardRef } from "react";

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate?: number | null;
}

interface InvoiceData {
  id: string;
  invoiceNumber?: string | null;
  issueDate?: string | null;
  dueDate?: string | null;
  totalAmount?: number | null;
  taxAmount?: number | null;
  status: string;
  notes?: string | null;
  sellerInfo?: string | null;
  buyerInfo?: string | null;
  items: InvoiceItem[];
  serviceOrder?: {
    id: string;
    title: string;
    description?: string | null;
    customer?: {
      id: string;
      name: string;
      email?: string | null;
      phone?: string | null;
      address?: string | null;
      city?: string | null;
      postalCode?: string | null;
      country?: string | null;
    } | null;
  } | null;
  companyInfo?: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website: string;
    taxId?: string;
    logo?: string;
    bankName?: string;
    bankAccountNumber?: string;
    bankSwift?: string;
    termsAndConditions?: string;
    invoiceFooter?: string;
  };
}

interface PrintableInvoiceProps {
  data: InvoiceData;
  showPrintButton?: boolean;
}

export const PrintableInvoice = forwardRef<HTMLDivElement, PrintableInvoiceProps>(
  ({ data, showPrintButton = false }, ref) => {
    const formatDate = (dateString: string | null | undefined) => {
      if (!dateString) return "N/A";
      return new Date(dateString).toLocaleDateString();
    };

    const formatCurrency = (amount: number | null | undefined) => {
      if (amount === null || amount === undefined) return "N/A";
      return new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(amount);
    };

    const handlePrint = () => {
      window.print();
    };

    // Parse seller and buyer info
    const sellerInfo = data.sellerInfo 
      ? (typeof data.sellerInfo === 'string' ? data.sellerInfo : JSON.stringify(data.sellerInfo))
      : null;
    
    const buyerInfo = data.buyerInfo
      ? (typeof data.buyerInfo === 'string' ? data.buyerInfo : JSON.stringify(data.buyerInfo))
      : null;

    // Calculate subtotal
    const subtotal = data.items.reduce((sum, item) => sum + item.totalPrice, 0);

    return (
      <div ref={ref} className="bg-white p-8 max-w-4xl mx-auto">
        {showPrintButton && (
          <div className="print:hidden mb-4 text-right">
            <button
              onClick={handlePrint}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            >
              Print Invoice
            </button>
          </div>
        )}

        {/* Header */}
        <div className="flex justify-between items-start mb-8 border-b pb-6">
          <div>
            {data.companyInfo?.logo ? (
              <img 
                src={data.companyInfo.logo} 
                alt="Company Logo" 
                className="h-16 mb-2" 
              />
            ) : (
              <h1 className="text-2xl font-bold">
                {data.companyInfo?.name || "HVAC Service Company"}
              </h1>
            )}
            <div className="text-gray-600">
              <p>{data.companyInfo?.address || "123 Main St, City, State 12345"}</p>
              <p>
                {data.companyInfo?.phone || "************"} | 
                {data.companyInfo?.email || "<EMAIL>"}
              </p>
              <p>{data.companyInfo?.website || "www.hvaccompany.com"}</p>
              {data.companyInfo?.taxId && <p>Tax ID: {data.companyInfo.taxId}</p>}
            </div>
          </div>
          <div className="text-right">
            <h2 className="text-xl font-bold">INVOICE</h2>
            <p className="text-gray-600">Invoice #: {data.invoiceNumber || data.id}</p>
            <p className="text-gray-600">Issue Date: {formatDate(data.issueDate)}</p>
            <p className="text-gray-600">Due Date: {formatDate(data.dueDate)}</p>
            <p className={`font-bold mt-2 ${
              data.status === 'PAID' ? 'text-green-600' : 
              data.status === 'OVERDUE' ? 'text-red-600' : 
              'text-yellow-600'
            }`}>
              {data.status}
            </p>
          </div>
        </div>

        {/* Bill To / Service Information */}
        <div className="grid grid-cols-2 gap-6 mb-8">
          <div className="border p-4 rounded">
            <h3 className="font-bold text-lg mb-2">Bill To</h3>
            {data.serviceOrder?.customer ? (
              <div>
                <p className="font-semibold">{data.serviceOrder.customer.name}</p>
                {data.serviceOrder.customer.address && (
                  <p>{data.serviceOrder.customer.address}</p>
                )}
                {data.serviceOrder.customer.city && data.serviceOrder.customer.postalCode && (
                  <p>
                    {data.serviceOrder.customer.city}, {data.serviceOrder.customer.postalCode}
                    {data.serviceOrder.customer.country ? `, ${data.serviceOrder.customer.country}` : ""}
                  </p>
                )}
                {data.serviceOrder.customer.phone && <p>Phone: {data.serviceOrder.customer.phone}</p>}
                {data.serviceOrder.customer.email && <p>Email: {data.serviceOrder.customer.email}</p>}
              </div>
            ) : buyerInfo ? (
              <div className="whitespace-pre-line">{buyerInfo}</div>
            ) : (
              <p className="text-gray-500">No customer information available</p>
            )}
          </div>

          <div className="border p-4 rounded">
            <h3 className="font-bold text-lg mb-2">Service Information</h3>
            {data.serviceOrder ? (
              <div>
                <p className="font-semibold">{data.serviceOrder.title}</p>
                {data.serviceOrder.description && (
                  <p className="whitespace-pre-line">{data.serviceOrder.description}</p>
                )}
              </div>
            ) : (
              <p className="text-gray-500">No service information available</p>
            )}
          </div>
        </div>

        {/* Invoice Items */}
        <div className="mb-8">
          <h3 className="font-bold text-lg mb-2 border-b pb-1">Invoice Items</h3>
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100">
                <th className="border p-2 text-left">Description</th>
                <th className="border p-2 text-right">Quantity</th>
                <th className="border p-2 text-right">Unit Price</th>
                <th className="border p-2 text-right">Tax Rate</th>
                <th className="border p-2 text-right">Total</th>
              </tr>
            </thead>
            <tbody>
              {data.items.map((item) => (
                <tr key={item.id}>
                  <td className="border p-2">{item.description}</td>
                  <td className="border p-2 text-right">{item.quantity}</td>
                  <td className="border p-2 text-right">{formatCurrency(item.unitPrice)}</td>
                  <td className="border p-2 text-right">{item.taxRate ? `${item.taxRate}%` : 'N/A'}</td>
                  <td className="border p-2 text-right">{formatCurrency(item.totalPrice)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Totals */}
        <div className="flex justify-end mb-8">
          <div className="w-64 border rounded p-4">
            <div className="flex justify-between mb-2">
              <span>Subtotal:</span>
              <span>{formatCurrency(subtotal)}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Tax:</span>
              <span>{formatCurrency(data.taxAmount)}</span>
            </div>
            <div className="flex justify-between font-bold text-lg border-t pt-2 mt-2">
              <span>Total:</span>
              <span>{formatCurrency(data.totalAmount)}</span>
            </div>
          </div>
        </div>

        {/* Notes */}
        {data.notes && (
          <div className="mb-8">
            <h3 className="font-bold text-lg mb-2 border-b pb-1">Notes</h3>
            <p className="whitespace-pre-line">{data.notes}</p>
          </div>
        )}

        {/* Payment Information */}
        <div className="mb-8 border p-4 rounded">
          <h3 className="font-bold text-lg mb-2">Payment Information</h3>
          <p>Please make payment to:</p>
          <p className="font-semibold">{data.companyInfo?.name || "HVAC Service Company"}</p>
          <p>Bank: {data.companyInfo?.bankName || "Example Bank"}</p>
          <p>Account: {data.companyInfo?.bankAccountNumber || "1234 5678 9012 3456"}</p>
          {data.companyInfo?.bankSwift && <p>SWIFT/BIC: {data.companyInfo.bankSwift}</p>}
          <p>Reference: Invoice #{data.invoiceNumber || data.id}</p>
          
          {data.companyInfo?.termsAndConditions && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <p className="font-semibold">Terms and Conditions:</p>
              <p className="text-sm">{data.companyInfo.termsAndConditions}</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="text-center text-gray-500 text-sm mt-8 pt-4 border-t">
          <p>{data.companyInfo?.invoiceFooter || "Thank you for your business!"}</p>
          <p>This invoice was generated on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}</p>
        </div>
      </div>
    );
  }
);

PrintableInvoice.displayName = "PrintableInvoice";
