import { Link } from "@remix-run/react"
import { Card } from "~/components/ui/card"
import { Button } from "~/components/ui/button"
import { Badge } from "~/components/ui/badge"
import type { Device } from "@prisma/client"

interface DeviceHealthIndicatorProps {
  device: Device & {
    healthScore?: number,
    lastMaintenanceDate?: string,
    nextMaintenanceDate?: string,
    failureProbability?: number,
    predictedComponent?: string
  }
  compact?: boolean
  showActions?: boolean
}

export function DeviceHealthIndicator({
  device,
  compact = false,
  showActions = true
}: DeviceHealthIndicatorProps) {
  // Health score color mapping
  const getHealthColor = (score: number) => {
    if (score >= 80) return 'bg-green-500 text-white'
    if (score >= 50) return 'bg-yellow-500 text-white'
    return 'bg-red-500 text-white'
  }

  // Failure probability color mapping
  const getFailureProbabilityColor = (probability: number) => {
    if (probability < 0.3) return 'bg-green-500 text-white'
    if (probability < 0.7) return 'bg-yellow-500 text-white'
    return 'bg-red-500 text-white'
  }

  // Format date or return placeholder
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Brak danych'
    return new Date(dateString).toLocaleDateString()
  }

  // Calculate days until next maintenance
  const getDaysUntilMaintenance = () => {
    if (!device.nextMaintenanceDate) return null

    const today = new Date()
    const nextMaintenance = new Date(device.nextMaintenanceDate)
    const diffTime = nextMaintenance.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    return diffDays
  }

  const daysUntilMaintenance = getDaysUntilMaintenance()

  if (compact) {
    return (
      <div className="flex items-center space-x-2">
        <div
          className={`w-3 h-3 rounded-full ${
            device.healthScore
              ? device.healthScore >= 80
                ? 'bg-green-500'
                : device.healthScore >= 50
                  ? 'bg-yellow-500'
                  : 'bg-red-500'
              : 'bg-gray-500'
          }`}
        />
        <span className="text-sm">
          {device.healthScore
            ? `${device.healthScore}% sprawności`
            : 'Brak danych o stanie'}
        </span>
      </div>
    )
  }

  return (
    <Card className="p-4">
      <div className="flex justify-between items-start mb-3">
        <h3 className="font-medium text-lg">Stan urządzenia</h3>
        {device.healthScore && (
          <Badge className={getHealthColor(device.healthScore)}>
            {device.healthScore}% sprawności
          </Badge>
        )}
      </div>

      <div className="space-y-2 mb-4">
        {device.failureProbability !== undefined && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500 dark:text-gray-400">Prawdopodobieństwo awarii:</span>
            <Badge className={getFailureProbabilityColor(device.failureProbability)}>
              {Math.round(device.failureProbability * 100)}%
            </Badge>
          </div>
        )}

        {device.predictedComponent && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500 dark:text-gray-400">Zagrożony komponent:</span>
            <span className="font-medium">{device.predictedComponent}</span>
          </div>
        )}

        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500 dark:text-gray-400">Ostatni serwis:</span>
          <span>{formatDate(device.lastMaintenanceDate)}</span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500 dark:text-gray-400">Następny serwis:</span>
          <span className="flex items-center">
            {formatDate(device.nextMaintenanceDate)}
            {daysUntilMaintenance !== null && daysUntilMaintenance <= 14 && (
              <Badge className="ml-2 bg-yellow-500 text-white">
                {daysUntilMaintenance <= 0
                  ? 'Zaległy!'
                  : `Za ${daysUntilMaintenance} dni`}
              </Badge>
            )}
          </span>
        </div>
      </div>

      {showActions && (
        <div className="flex space-x-2">
          <Link
            to={`/devices/${device.id}/predictions`}
            className="flex-1 inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3"
          >
            Szczegóły predykcji
          </Link>
          <Link
            to={`/service-orders/new?deviceId=${device.id}&type=MAINTENANCE`}
            className="flex-1 inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3"
          >
            Zaplanuj serwis
          </Link>
        </div>
      )}
    </Card>
  )
}
