import { json, type LoaderFunctionArgs, type ActionFunctionArgs, redirect } from "@remix-run/node";
import { useLoaderData, useNavigate, Link } from "@remix-run/react";
import { requireUserId } from "~/session.server";
import { getDeviceById, deleteDevice } from "~/services/device.service";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { Badge } from "~/components/ui/badge";
import { DeviceHealthSummary } from "~/components/molecules/device";
import { RichTextDisplay, stringToPlateValue } from "~/components/ui/rich-text-editor";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const { deviceId } = params;

  if (!deviceId) {
    throw new Response("Device ID is required", { status: 400 });
  }

  const deviceResponse = await getDeviceById(deviceId, userId);

  if (!deviceResponse.success || !deviceResponse.data) {
    throw new Response(deviceResponse.error || "Device not found", { status: 404 });
  }

  return json({ device: deviceResponse.data });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const { deviceId } = params;

  if (!deviceId) {
    throw new Response("Device ID is required", { status: 400 });
  }

  const formData = await request.formData();
  const intent = formData.get("intent");

  if (intent === "delete") {
    const deleteResponse = await deleteDevice(deviceId, userId);

    if (!deleteResponse.success) {
      return json({ error: deleteResponse.error }, { status: 400 });
    }

    return redirect("/devices");
  }

  return json({ error: "Invalid intent" }, { status: 400 });
}

export default function DeviceDetailPage() {
  const { device } = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  // Handle delete confirmation
  const handleDelete = () => {
    if (confirm("Are you sure you want to delete this device? This action cannot be undone.")) {
      const form = document.createElement("form");
      form.method = "post";
      form.appendChild(createHiddenInput("intent", "delete"));
      document.body.appendChild(form);
      form.submit();
    }
  };

  // Helper to create hidden input
  const createHiddenInput = (name: string, value: string) => {
    const input = document.createElement("input");
    input.type = "hidden";
    input.name = name;
    input.value = value;
    return input;
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to="/devices" className="text-blue-500 hover:underline">
          ← Back to Devices
        </Link>
      </div>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{device.name}</h1>
        <div className="flex gap-2">
          <Link to={`/devices/${device.id}/telemetry`}>
            <Button variant="outline">Telemetry</Button>
          </Link>
          <Link to={`/devices/${device.id}/predictions`}>
            <Button variant="outline">Predictions</Button>
          </Link>
          <Link to={`/devices/${device.id}/edit`}>
            <Button variant="outline">Edit</Button>
          </Link>
          <Button variant="destructive" onClick={handleDelete}>
            Delete
          </Button>
        </div>
      </div>

      {/* Device Health Summary */}
      {device.telemetry && device.predictions && (
        <DeviceHealthSummary
          deviceId={device.id}
          telemetry={device.telemetry}
          predictions={device.predictions}
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Device Information */}
        <Card>
          <CardHeader>
            <CardTitle>Device Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Name</Label>
              <p className="text-lg">{device.name}</p>
            </div>

            <div>
              <Label>Model</Label>
              <p>{device.model || "Not specified"}</p>
            </div>

            <div>
              <Label>Serial Number</Label>
              <p>{device.serialNumber || "Not specified"}</p>
            </div>

            <div>
              <Label>Manufacturer</Label>
              <p>{device.manufacturer || "Not specified"}</p>
            </div>

            <div>
              <Label>Installation Date</Label>
              <p>{formatDate(device.installationDate)}</p>
            </div>

            <div>
              <Label>Warranty Expiry Date</Label>
              <p>{formatDate(device.warrantyExpiryDate)}</p>
            </div>

            {device.notes && (
              <div>
                <Label>Notes</Label>
                <RichTextDisplay
                  value={stringToPlateValue(device.notes)}
                  minHeight="auto"
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent>
            {device.customer ? (
              <div className="space-y-4">
                <div>
                  <Label>Name</Label>
                  <p>
                    <Link to={`/customers/${device.customer.id}`} className="text-blue-500 hover:underline">
                      {device.customer.name}
                    </Link>
                  </p>
                </div>

                {device.customer.email && (
                  <div>
                    <Label>Email</Label>
                    <p>{device.customer.email}</p>
                  </div>
                )}

                {device.customer.phone && (
                  <div>
                    <Label>Phone</Label>
                    <p>{device.customer.phone}</p>
                  </div>
                )}

                {device.customer.address && (
                  <div>
                    <Label>Address</Label>
                    <p>{device.customer.address}</p>
                    {device.customer.city && device.customer.postalCode && (
                      <p>
                        {device.customer.city}, {device.customer.postalCode}
                        {device.customer.country ? `, ${device.customer.country}` : ""}
                      </p>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500">No customer information available</p>
            )}
          </CardContent>
        </Card>

        {/* Service Orders */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Service Orders</CardTitle>
            <CardDescription>
              {device.serviceOrders.length} service order(s)
            </CardDescription>
          </CardHeader>
          <CardContent>
            {device.serviceOrders.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2 px-4">Title</th>
                      <th className="text-left py-2 px-4">Status</th>
                      <th className="text-left py-2 px-4">Priority</th>
                      <th className="text-left py-2 px-4">Scheduled Date</th>
                      <th className="text-left py-2 px-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {device.serviceOrders.map((order) => (
                      <tr key={order.id} className="border-b hover:bg-gray-50">
                        <td className="py-2 px-4">
                          <Link to={`/service-orders/${order.id}`} className="hover:underline">
                            {order.title}
                          </Link>
                        </td>
                        <td className="py-2 px-4">
                          <span className={`px-2 py-1 rounded text-xs ${getStatusColor(order.status)}`}>
                            {order.status}
                          </span>
                        </td>
                        <td className="py-2 px-4">
                          <span className={`px-2 py-1 rounded text-xs ${getPriorityColor(order.priority)}`}>
                            {order.priority}
                          </span>
                        </td>
                        <td className="py-2 px-4">
                          {order.scheduledDate ? new Date(order.scheduledDate).toLocaleDateString() : "Not scheduled"}
                        </td>
                        <td className="py-2 px-4">
                          <Link to={`/service-orders/${order.id}/edit`}>
                            <Button variant="outline" size="sm">Edit</Button>
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-500">No service orders</p>
            )}
          </CardContent>
          <CardFooter>
            <Link to={`/service-orders/new?deviceId=${device.id}${device.customer ? `&customerId=${device.customer.id}` : ''}`}>
              <Button variant="outline">Create Service Order</Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}

// Helper function to get status color
function getStatusColor(status: string) {
  switch (status.toUpperCase()) {
    case "PENDING":
      return "bg-yellow-100 text-yellow-800";
    case "IN_PROGRESS":
      return "bg-blue-100 text-blue-800";
    case "COMPLETED":
      return "bg-green-100 text-green-800";
    case "CANCELLED":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Helper function to get priority color
function getPriorityColor(priority: string) {
  switch (priority.toUpperCase()) {
    case "LOW":
      return "bg-green-100 text-green-800";
    case "MEDIUM":
      return "bg-blue-100 text-blue-800";
    case "HIGH":
      return "bg-orange-100 text-orange-800";
    case "URGENT":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}
